using BlackRoseServer.Helper;
using HarmonyLib;
using LabApi.Features.Wrappers;
using PlayerStatsSystem;

namespace BlackRoseServer.Patches
{
    [HarmonyPatch(typeof(HealthStat), nameof(HealthStat.MaxValue), MethodType.Getter)]
    public static class MaxHealthGetPatch
    {
        public static bool Prefix(ref float __result, HealthStat __instance)
        {
            if (XHelper.healthDict.TryGetValue(Player.Get(__instance.Hub).Role, out var health))
            {
                __result = health;
                return false;
            }
            return true;
        }
    }
}